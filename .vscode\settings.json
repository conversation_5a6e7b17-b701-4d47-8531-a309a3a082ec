{"i18n-ally.localesPaths": ["lang"], "i18n-ally.keystyle": "flat", "cSpell.words": ["<PERSON><PERSON><PERSON><PERSON>", "appstores", "AVARY", "CARETEAM", "chatbots", "closebutton", "composables", "CONSOLA", "descr", "echarts", "greyonhov<PERSON>color", "healthgpt", "j<PERSON>ld", "KEYCLOAK", "muot", "nuxi", "nuxt", "nuxtjs", "pinia", "primevue", "<PERSON><PERSON>", "sidebase", "sprites", "tailwindcss", "topwrapper", "touchless", "typecheck", "uptitle", "userpic", "valign", "vuelidate", "vueuse", "zadigetvoltaire", "<PERSON><PERSON><PERSON>", "healthtalk", "dompurify", "blockstring", "chinh-sach", "dieu-k<PERSON>an", "thuong"], "tailwindCSS.experimental.classRegex": ["tw`(.*?)`", "tw\\('(.*?)'\\)"], "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "editor.codeActionsOnSave": {"source.fixAll": "never", "source.fixAll.eslint": "never"}}