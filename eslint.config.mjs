// @ts-check
import with<PERSON>uxt from './.nuxt/eslint.config.mjs'

export default withNuxt({
  ignores: ['assets'],
}).overrideRules({
  '@typescript-eslint/no-explicit-any': 'off',
  '@typescript-eslint/no-empty-object-type': 'off',
  'vue/singleline-html-element-content-newline': 'off',
  'vue/html-self-closing': [
    'error',
    {
      html: {
        void: 'always',
        normal: 'never',
        component: 'always',
      },
      svg: 'always',
      math: 'always',
    },
  ],
})
