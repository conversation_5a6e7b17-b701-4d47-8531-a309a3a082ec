// @ts-check
import with<PERSON>uxt from './.nuxt/eslint.config.mjs'

export default withNuxt({
  ignores: ['assets'],
}).overrideRules({
  '@typescript-eslint/no-explicit-any': 'off',
  '@typescript-eslint/no-empty-object-type': 'off',
  'vue/singleline-html-element-content-newline': 'off',
  'vue/html-self-closing': [
    'error',
    {
      html: {
        void: 'always', // Các thẻ void như <img>, <br>, <hr> phải tự đóng
        normal: 'never', // Các thẻ HTML thông thường như <div>, <p> không được tự đóng
        component: 'always', // Các component Vue phải tự đóng nếu không có nội dung
      },
      svg: 'always',
      math: 'always',
    },
  ],
})
