<script setup lang="ts">
import { ElasticIndex } from '~/models'
import { useJsonldHomePage } from '~/composables'

defineI18nRoute({
  paths: {
    en: '/home',
  },
})

const { t, locale } = useI18n({ useScope: 'local' })
useSeoMeta({
  title: t('title'),
  ogTitle: t('title'),
  description: t('meta:description'),
  ogDescription: t('meta:description'),
  ogLocale: locale,
})
useJsonldHomePage()

// const features = [
//   {
//     data: '17.7',
//     unit: t('traction:1:unit'),
//     description: t('traction:1:description'),
//   },
//   {
//     data: '13.7',
//     unit: t('traction:2:unit'),
//     description: t('traction:2:description'),
//   },
//   {
//     data: '40',
//     unit: t('traction:3:unit'),
//     description: t('traction:3:description'),
//   },
//   {
//     data: '100',
//     unit: t('traction:4:unit'),
//     description: t('traction:4:description'),
//   },
// ]

const services = [
  {
    video: {
      src: 'https://storage.googleapis.com/cms-gallery/673aaaecb3f516cfbf53bd38/bacsiriengnew.mp4',
      type: 'video/mp4',
    },
    name: 'Bác sĩ riêng',
    icon: 'pi pi-envelope',
    title: t('services:1:title'),
    subTitle: t('services:1:subtitle'),
  },
  {
    video: {
      src: 'https://storage.googleapis.com/patient-education/66553db0f2fbd40696cca6f8/original.mp4',
      type: 'video/mp4',
    },
    name: 'Khám từ xa',
    icon: 'pi pi-envelope',
    title: t('services:2:title'),
    subTitle: t('services:2:subtitle'),
  },
  {
    video: {
      src: 'https://storage.googleapis.com/patient-education/66553db4f2fbd4f485cca70a/original.mp4',
      type: 'video/mp4',
    },
    name: 'EduHub',
    icon: 'pi pi-envelope',
    title: t('services:3:title'),
    subTitle: t('services:3:subtitle'),
  },
  {
    video: {
      src: 'https://storage.googleapis.com/patient-education/66553db6f2fbd45f9dcca719/original.mp4',
      type: 'video/mp4',
    },
    name: 'HealthGPT',
    icon: 'pi pi-envelope',
    title: t('services:4:title'),
    subTitle: t('services:4:subtitle'),
  },
]

// const plans = [
//   {
//     name: t("plans:1:name"),
//     price: "50k",
//     per: t("plans:fee-per-use"),
//     descriptionBottom: t("plans:1:description-bottom"),
//     benefits: t("plans:1:benefits").split("."),
//     actionName: t("plans:1:cta:name"),
//     actionRoute: locale.value === "en" ? "en/download" : "/download",
//   },
//   {
//     name: t("plans:2:name"),
//     price: "250k",
//     per: t("plans:fee-per-year"),
//     descriptionBottom: t("plans:2:description-bottom"),
//     benefits: t("plans:2:benefits").split("."),
//     actionName: t("plans:2:cta:name"),
//     actionRoute:
//       locale.value === "en"
//         ? "/en/dich-vu/dang-ky-thanh-vien"
//         : "/dich-vu/dang-ky-thanh-vien",
//   },
//   {
//     name: t("plans:3:name"),
//     price: t("plans:3:fee"),
//     benefits: t("plans:3:benefits").split("."),
//     actionName: t("plans:3:cta:name"),
//     actionRoute: locale.value === "en" ? "/en/contact" : "/lien-he",
//   },
// ];
// const isOpen = ref<boolean>(false)
// setTimeout(() => {
//   isOpen.value = true
// })
</script>

<template>
  <div>
    <!-- <LunnarNewYearVideoModal
      :is-open="isOpen"
      @close="isOpen = false"
    /> -->

    <!-- 59 elements -->
    <PageHomeImageCarousel />

    <!-- 35 elements -->
    <PageHomeOurPartner />

    <PageHomeHealthGPT />

    <!-- 235 elements -->
    <BlockFeatureMobileApp
      :features="services"
      :title="t('services:title')"
      :subtitle="t('services:subtitle')"
    />

    <PageHomeOurTeam />

    <h2 class="mb-8 text-center">What our customers say about us</h2>
    <!-- 191 elements -->
    <TestimonialBlock
      :elastic-index="ElasticIndex.RATINGS_HOMEPAGE"
      :container="{
        class: 'px-4 sm:px-6 lg:px-8 2xl:px-24',
      }"
    />

    <!-- 90 elements -->
    <!-- <WPricingTable
      :blocks="['title', 'sub-title']"
      :title="t('plans:title')"
      :sub-title="t('plans:subtitle')"
      :items="plans"
      :button-passthrough="{
        root: {
          style: 'padding: 12px 16px',
        },
      }"
      @on-click="(item: any) => $router.push(item.actionRoute)"
    /> -->
  </div>
</template>

<i18n lang="yaml">
en:
  'cover:subtitle': 'You are in pregnancy, having small kids, or in need manange a health condition, Wellcare is here to help'
  'cover:title': 'Wellcare - Your TRUSTED Health Partner'
  'doctors:1:name': 'Nguyen Tri Doan'
  'doctors:1:role': 'Dr. Tri Doan is a renowned pediatrician known for his consultative approach that avoids overuse of medication and tests, based on evidence-based medicine. His unique approach to problem-solving is summarized in the book "Let the Kids Get Sick", inspiring millions of parents and thousands of other doctors to follow.'
  'doctors:2:name': 'Truong Huu Khanh'
  'doctors:2:role': 'Dr. Khanh is a highly influential figure in the medical field, especially in infectious diseases. Known for his humble and compassionate style, he spends much of his time educating the community on health through the "Ask Pediatric Doctors" page, followed by nearly 300,000 people.'
  'doctors:3:name': 'Phan Quoc Bao'
  'doctors:3:role': 'A doctor serving at the University Hospital of Medicine and Pharmacy in Ho Chi Minh City, with over 20 years of experience in consulting and treating ENT diseases.'
  'doctors:4:name': 'Tran Thi Hong An'
  'doctors:4:role': 'A medical master specializing in General Internal Medicine, with nearly 30 years of experience. Completed her course at Sanford Medical Center in South Dakota, USA, focusing on Internal Medicine, Respiratory, Rheumatology, and Emergency Medicine.'
  'doctors:subtitle': 'The Pride of Wellcare'
  'doctors:title': 'Featured Doctors'
  'featured-doctors:subtitle': 'The Pride of Wellcare'
  'featured-doctors:title': 'Featured Doctors'
  'meta:description': 'Telemedicine, personal doctor, psychology therapy, second opinions from trusted doctors and psychologists'
  'plans:1:benefits': 'Doctor fee paid per use. Personal health records for all family members. Telemedicine. Health Question and Answer. Second Opinions'
  'plans:1:cta:name': 'Download Wellcare App Now'
  'plans:1:name': 'Pay As You Go'
  'plans:1:description-bottom': 'Consultation fee + 50k/booking Service fee'
  'plans:2:benefits': 'Doctor fee paid per use. Waive service fees. Personal Doctor. HealthGPT. EduHub. Health Programs'
  'plans:2:cta:name': 'Become A Member Now'
  'plans:2:name': 'Member'
  'plans:2:description-bottom': 'Consultation fee + 0d Service fee (waived)'
  'plans:3:benefits': 'Volume Discounts. Customized Benefits. App Integration and White labeling. Premium Support'
  'plans:3:cta:name': 'Request A Quote'
  'plans:3:fee': 'Contact'
  'plans:3:name': 'Enterprise'
  'plans:fee-per-use': 'đ/use'
  'plans:fee-per-year': 'đ/year'
  'plans:subtitle': 'Choose what works best for you!'
  'plans:title': 'Our Plans'
  'services:1:subtitle': 'Caring for your loved ones'
  'services:1:title': 'Personal Doctor'
  'services:2:subtitle': 'Online or Offline, it is thorough examination that matters!'
  'services:2:title': 'Telemedicine'
  'services:3:subtitle': 'Access knowledge, Healthtalks, Video trainings'
  'services:3:title': 'EduHub'
  'services:4:subtitle': 'Chat 24/7 with AI doctors'
  'services:4:title': 'HealthGPT'
  'services:subtitle': 'Seamless access, effective treatment, reduced cost'
  'services:title': 'Digital Health Services'
  'title': 'Wellcare - Your TRUSTED digital health companion'
vi:
  'cover:subtitle': 'Bạn đang mang thai, có con nhỏ, hay cần kiểm soát vấn đề sức khoẻ thể chất hoặc tinh thần, hãy để Wellcare giúp bạn.'
  'cover:title': 'Wellcare - Đối tác sức khỏe tin cậy của bạn'
  'doctors:1:name': 'Nguyễn Trí Đoàn'
  'doctors:1:role': 'Bs Trí Đoàn là một bác sĩ Nhi nổi tiếng với phong cách tư vấn bệnh không lạm dụng thuốc và xét nghiệm, dựa trên y học chứng cứ. Cách tiếp cận vấn đề đặc biệt của bác sĩ được tóm lược trong cuốn “Để con được ốm” và đã truyền cảm hứng cho hàng triệu phụ huynh và hàng ngàn bác sĩ khác thực hành theo.'
  'doctors:2:name': 'Trương Hữu Khanh'
  'doctors:2:role': 'Bs Khanh là một người có tầm ảnh hưởng lớn trong ngành y, đặc biệt về bệnh truyền nhiễm. Được biết tới với một phong cách mộc mạc và nhân hậu, bác sĩ dành nhiều thời gian của mình để đào tạo sức khỏe cộng đồng thông qua trang “Hỏi bác sĩ Nhi đồng” với gần 300 ngàn người theo dõi.'
  'doctors:3:name': 'Phan Quốc Bảo'
  'doctors:3:role': 'Bác sĩ công tác tại Bệnh viện Đại học Y Dược Tp.HCM, có kinh nghiệm trên 20 năm trong tư vấn và điều trị các bệnh lý Tai Mũi Họng.'
  'doctors:4:name': 'Trần Thị Hồng An'
  'doctors:4:role': 'Thạc sĩ Y khoa chuyên ngành Nội tổng quát, có gần 30 năm kinh nghiệm trong nghề. Hoàn thành khóa học tại Sanford Medical Center bang South Dakota Hoa Kỳ về: Nội Khoa, Hô hấp, Khớp, Cấp cứu.'
  'doctors:subtitle': 'Niềm tự hào của Wellcare'
  'doctors:title': 'Bác sĩ nổi bật'
  'featured-doctors:subtitle': 'Niềm tự hào của Wellcare'
  'featured-doctors:title': 'Bác sĩ nổi bật'
  'meta:description': 'Khám từ xa, bác sĩ riêng, trị liệu tâm lý, ý kiến độc lập, giải đáp thắc mắc từ đội ngũ bác sĩ, tâm lý gia đáng tin cậy.'
  'plans:1:benefits': 'Khám từ xa. Câu hỏi kiến thức. Sổ sức khỏe cho cả gia đình (miễn phí)'
  'plans:1:cta:name': 'Tải Ứng Dụng Wellcare Ngay'
  'plans:1:name': 'Dịch vụ từng lần'
  'plans:1:description-bottom': 'Phí khám + 50k/lần Phí dịch vụ'
  'plans:2:benefits': 'Khám từ xa. Câu hỏi kiến thức. Sổ sức khỏe cho cả gia đình (miễn phí). Ý kiến độc lập. Bác sĩ riêng. Chương trình sức khỏe. HealthGPT (miễn phí). EduHub (miễn phí)'
  'plans:2:cta:name': 'Đăng ký Thành viên'
  'plans:2:name': 'Thành viên cả năm'
  'plans:2:description-bottom': 'Phí khám + MIỄN phí dịch vụ'
  'plans:3:benefits': 'Ưu đãi số lượng lớn. Quyền lợi sức khỏe thiết kế riêng. Tích hợp ứng dụng và nhãn trắng. Hotline hỗ trợ riêng'
  'plans:3:cta:name': 'Yêu Cầu Gửi Báo Giá'
  'plans:3:fee': 'Liên hệ'
  'plans:3:name': 'Doanh nghiệp'
  'plans:fee-per-use': 'đ/lần'
  'plans:fee-per-year': 'đ/năm'
  'plans:subtitle': 'Chọn phương án phù hợp nhất với bạn!'
  'plans:title': 'Gói Dịch vụ'
  'services:1:subtitle': 'Cho người ta thương yêu'
  'services:1:title': 'Bác sĩ riêng'
  'services:2:subtitle': 'Khám gần khám xa, cốt là khám kỹ!'
  'services:2:title': 'Khám từ xa'
  'services:3:subtitle': 'Hỏi kiến thức, Healthtalk, Video trainings'
  'services:3:title': 'EduHub'
  'services:4:subtitle': 'Chat 24/7 với bác sĩ AI'
  'services:4:title': 'HealthGPT'
  'services:subtitle': 'Tiếp cận đơn giản, điều trị hiệu quả, chi phí tiết kiệm'
  'services:title': 'Dịch vụ y tế từ xa'
  'title': 'Wellcare - Lựa chọn TIN CẬY cho sức khỏe của bạn'
</i18n>
